/*
 * 檔案: modules_voice_error_display.gs
 * 分類: modules
 * 功能: 語音錯誤信息顯示模組
 * 描述: 將語音功能錯誤信息顯示到 LINE 前端聊天窗口
 * 最後更新: 2025-07-13
 */

/**
 * 🚨 顯示語音功能錯誤到 LINE 前端
 * 根據用戶要求，將技術錯誤詳細信息顯示給用戶
 * @param {string} replyToken - LINE 回覆 token
 * @param {string} errorType - 錯誤類型
 * @param {object} errorDetails - 錯誤詳細信息
 */
function displayVoiceErrorToUser(replyToken, errorType, errorDetails) {
  try {
    let errorMessage = '🚨 語音功能錯誤報告\n\n';
    
    switch (errorType) {
      case 'TTS_QUOTA_EXCEEDED':
        errorMessage += `🔊 TTS 模型錯誤：
📊 錯誤代碼：429 (配額超限)
🎯 模型：${errorDetails.model || '未知'}
⏰ 重試時間：${errorDetails.retryDelay || '未知'}

📋 詳細信息：
您已超過當前配額限制，請檢查您的計費方案。

🔗 更多信息：
https://ai.google.dev/gemini-api/docs/rate-limits

💡 解決方案：
1. 等待配額重置（約 ${errorDetails.retryDelay || '40秒'}）
2. 升級到付費方案
3. 使用其他 API 金鑰`;
        break;
        
      case 'AUDIO_MODEL_NOT_FOUND':
        errorMessage += `🎙️ 對話音頻模型錯誤：
📊 錯誤代碼：404 (模型不存在)
🎯 模型：${errorDetails.model || '未知'}
🔧 API 版本：${errorDetails.apiVersion || '未知'}

📋 詳細信息：
模型在指定的 API 版本中不存在，或不支援 generateContent 方法。

💡 可能原因：
1. 模型名稱錯誤
2. API 金鑰沒有語音功能權限
3. 需要申請 Gemini 語音功能 Beta 存取權限
4. 地區限制

🔧 建議解決方案：
1. 檢查 API 金鑰權限
2. 聯繫 Google 申請語音功能存取權限
3. 嘗試使用其他可用的語音模型`;
        break;
        
      case 'TTS_FORMAT_ERROR':
        errorMessage += `🔊 TTS 請求格式錯誤：
📊 錯誤代碼：400 (請求格式錯誤)
🎯 模型：${errorDetails.model || '未知'}

📋 詳細信息：
TTS 模型只接受 AUDIO 回應模式，不支援 TEXT 模式。

🔧 技術細節：
請求必須使用 responseModalities: ["AUDIO"]

💡 解決方案：
開發團隊已修正請求格式，請稍後重試。`;
        break;
        
      case 'API_KEY_ERROR':
        errorMessage += `🔑 API 金鑰錯誤：
📊 錯誤代碼：${errorDetails.statusCode || '未知'}

📋 詳細信息：
API 金鑰無效或權限不足。

💡 解決方案：
1. 檢查 API 金鑰是否正確
2. 確認 API 金鑰有語音功能權限
3. 聯繫管理員更新 API 金鑰`;
        break;
        
      default:
        errorMessage += `❌ 未知語音錯誤：
📊 錯誤代碼：${errorDetails.statusCode || '未知'}
🎯 模型：${errorDetails.model || '未知'}

📋 詳細信息：
${errorDetails.message || '未知錯誤'}

💡 建議：
請將此錯誤信息回報給開發團隊。`;
    }
    
    errorMessage += '\n\n⚠️ 請將此技術錯誤信息回報給開發團隊進行修復。';
    
    // 發送錯誤信息到 LINE
    const config = getConfig();
    const url = 'https://api.line.me/v2/bot/message/reply';
    
    const payload = JSON.stringify({
      replyToken: replyToken,
      messages: [{
        type: 'text',
        text: errorMessage
      }]
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.lineChannelAccessToken}`
      },
      payload: payload
    });
    
    if (response.getResponseCode() === 200) {
      console.log('✅ 語音錯誤信息已發送到 LINE 前端');
      
      // 記錄活動日誌
      logActivity('System', '語音錯誤顯示', 'Success', 'error_display', 'displayVoiceErrorToUser',
                  `錯誤類型: ${errorType}, 模型: ${errorDetails.model || '未知'}`);
    } else {
      console.error('❌ 發送語音錯誤信息失敗:', response.getContentText());
    }
    
  } catch (error) {
    console.error('❌ 顯示語音錯誤失敗:', error);
  }
}

/**
 * 🔧 根據錯誤代碼自動判斷錯誤類型
 * @param {number} statusCode - HTTP 狀態碼
 * @param {string} model - 模型名稱
 * @param {string} errorMessage - 錯誤信息
 * @returns {string} 錯誤類型
 */
function determineVoiceErrorType(statusCode, model, errorMessage) {
  if (statusCode === 429) {
    return 'TTS_QUOTA_EXCEEDED';
  }
  
  if (statusCode === 404 && model && model.includes('audio')) {
    return 'AUDIO_MODEL_NOT_FOUND';
  }
  
  if (statusCode === 400 && errorMessage && errorMessage.includes('response modalities')) {
    return 'TTS_FORMAT_ERROR';
  }
  
  if (statusCode === 401 || statusCode === 403) {
    return 'API_KEY_ERROR';
  }
  
  return 'UNKNOWN_ERROR';
}

/**
 * 🚨 語音功能錯誤處理包裝器
 * 統一處理語音功能錯誤並顯示到前端
 * @param {string} replyToken - LINE 回覆 token
 * @param {object} error - 錯誤對象
 * @param {string} model - 模型名稱
 * @param {string} apiVersion - API 版本
 */
function handleVoiceErrorWithDisplay(replyToken, error, model, apiVersion) {
  try {
    let statusCode, errorMessage, retryDelay;
    
    // 解析錯誤信息
    if (typeof error === 'object' && error.error) {
      statusCode = error.error.code;
      errorMessage = error.error.message;
      
      // 提取重試延遲時間
      if (error.error.details) {
        const retryInfo = error.error.details.find(detail => 
          detail['@type'] === 'type.googleapis.com/google.rpc.RetryInfo'
        );
        if (retryInfo && retryInfo.retryDelay) {
          retryDelay = retryInfo.retryDelay;
        }
      }
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    
    // 判斷錯誤類型
    const errorType = determineVoiceErrorType(statusCode, model, errorMessage);
    
    // 準備錯誤詳細信息
    const errorDetails = {
      model,
      apiVersion,
      statusCode,
      message: errorMessage,
      retryDelay
    };
    
    // 顯示錯誤到前端
    displayVoiceErrorToUser(replyToken, errorType, errorDetails);
    
    // 記錄詳細錯誤日誌
    console.error(`🚨 語音功能錯誤 [${errorType}]:`, {
      model,
      apiVersion,
      statusCode,
      errorMessage,
      retryDelay
    });
    
  } catch (displayError) {
    console.error('❌ 處理語音錯誤顯示失敗:', displayError);
    
    // 備用簡單錯誤信息
    const fallbackMessage = `🚨 語音功能發生錯誤\n\n模型: ${model}\n錯誤: ${error}\n\n請將此信息回報給開發團隊。`;
    
    try {
      const config = getConfig();
      const url = 'https://api.line.me/v2/bot/message/reply';
      
      const payload = JSON.stringify({
        replyToken: replyToken,
        messages: [{
          type: 'text',
          text: fallbackMessage
        }]
      });
      
      UrlFetchApp.fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.lineChannelAccessToken}`
        },
        payload: payload
      });
      
    } catch (fallbackError) {
      console.error('❌ 備用錯誤顯示也失敗:', fallbackError);
    }
  }
}
