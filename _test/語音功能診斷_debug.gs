/*
 * 檔案: 語音功能診斷_debug.gs
 * 分類: test
 * 功能: 語音功能診斷工具
 * 描述: 快速檢查語音功能的各個組件狀態
 * 最後更新: 2025-07-13 - 從 modules_audio_handler.gs 移至測試文件夾
 */

/**
 * 🔍 語音功能診斷工具
 * 用於快速檢查語音功能的各個組件狀態
 */
function diagnoseVoiceFailure_debug() {
  console.log('🔍 === 語音功能診斷開始 ===');

  const diagnosis = {
    timestamp: new Date().toISOString(),
    checks: {},
    recommendations: []
  };

  // 1. 檢查配置
  try {
    const config = getConfig();
    diagnosis.checks.apiKey = !!config.geminiApiKey;
    diagnosis.checks.ttsModel = config.ttsModel;
    diagnosis.checks.audioDialogModel = config.audioDialogModel;
    diagnosis.checks.configStatus = 'OK';
  } catch (error) {
    diagnosis.checks.configError = error.message;
    diagnosis.checks.configStatus = 'FAILED';
  }

  // 2. 檢查功能開關
  try {
    diagnosis.checks.conversationalAudioEnabled = isFeatureEnabled('CONVERSATIONAL_AUDIO');
    diagnosis.checks.ttsEnabled = isFeatureEnabled('TEXT_TO_SPEECH');
    diagnosis.checks.featureToggleStatus = 'OK';
  } catch (error) {
    diagnosis.checks.featureToggleError = error.message;
    diagnosis.checks.featureToggleStatus = 'FAILED';
  }

  // 3. 測試模型調用
  try {
    const testResult = callGeminiAudioDialog('測試語音', {});
    diagnosis.checks.audioDialogTest = {
      success: testResult.success,
      error: testResult.error,
      modelUsed: testResult.modelUsed
    };
    diagnosis.checks.audioDialogStatus = testResult.success ? 'OK' : 'FAILED';
  } catch (error) {
    diagnosis.checks.audioDialogTestError = error.message;
    diagnosis.checks.audioDialogStatus = 'ERROR';
  }

  // 4. 生成建議
  if (diagnosis.checks.configStatus === 'FAILED') {
    diagnosis.recommendations.push('檢查 APIKEY 工作表配置');
  }
  if (diagnosis.checks.featureToggleStatus === 'FAILED') {
    diagnosis.recommendations.push('檢查功能開關設定');
  }
  if (diagnosis.checks.audioDialogStatus !== 'OK') {
    diagnosis.recommendations.push('檢查語音模型配置和 API 金鑰');
  }

  console.log('📊 語音診斷結果:', JSON.stringify(diagnosis, null, 2));
  return diagnosis;
}

/**
 * 🔊 TTS 模型正確測試方法
 * 使用正確的 AUDIO 回應模式測試 TTS 模型
 */
function TTS模型正確測試_debug() {
  console.log('🔊 ===== TTS 模型正確測試開始 =====');
  
  try {
    const config = getConfig();
    const ttsModel = config.ttsModel;
    const apiKey = getCurrentGeminiApiKey();
    
    console.log(`測試 TTS 模型: ${ttsModel}`);
    
    const apiVersion = getModelApiVersion(ttsModel);
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${ttsModel}:generateContent?key=${apiKey}`;
    
    // 🔧 正確的 TTS 請求格式：使用 AUDIO 回應模式
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: '測試語音合成' }]
      }],
      generationConfig: {
        responseModalities: ["AUDIO"],  // 關鍵：必須是 AUDIO，不是 TEXT
        speechConfig: {
          voiceConfig: {
            prebuiltVoiceConfig: {
              voiceName: 'Kore'
            }
          }
        }
      }
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    const statusCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`TTS 測試結果: ${statusCode}`);
    
    if (statusCode === 200) {
      console.log('✅ TTS 模型可用');
      return { success: true, model: ttsModel };
    } else {
      console.log(`❌ TTS 模型不可用: ${responseText}`);
      return { success: false, model: ttsModel, error: responseText };
    }
    
  } catch (error) {
    console.error('❌ TTS 測試失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 🎙️ 對話音頻模型正確測試方法
 * 使用正確的音頻對話請求格式
 */
function 對話音頻模型正確測試_debug() {
  console.log('🎙️ ===== 對話音頻模型正確測試開始 =====');
  
  try {
    const config = getConfig();
    const audioDialogModel = config.audioDialogModel;
    const apiKey = getCurrentGeminiApiKey();
    
    console.log(`測試對話音頻模型: ${audioDialogModel}`);
    
    const apiVersion = getModelApiVersion(audioDialogModel);
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${audioDialogModel}:generateContent?key=${apiKey}`;
    
    // 🔧 正確的對話音頻請求格式
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: '你好，請用語音回覆' }]
      }],
      generationConfig: {
        responseModalities: ["TEXT", "AUDIO"]  // 對話音頻支援文字和音頻
      }
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    const statusCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`對話音頻測試結果: ${statusCode}`);
    
    if (statusCode === 200) {
      console.log('✅ 對話音頻模型可用');
      return { success: true, model: audioDialogModel };
    } else {
      console.log(`❌ 對話音頻模型不可用: ${responseText}`);
      return { success: false, model: audioDialogModel, error: responseText };
    }
    
  } catch (error) {
    console.error('❌ 對話音頻測試失敗:', error);
    return { success: false, error: error.message };
  }
}

/**
 * 🚀 完整語音功能測試_debug
 * 使用正確的請求格式測試所有語音功能
 */
function 完整語音功能測試_debug() {
  console.log('🚀 ===== 完整語音功能測試開始 =====');
  
  const results = {
    timestamp: new Date().toISOString(),
    tests: {},
    summary: {
      total: 0,
      passed: 0,
      failed: 0
    }
  };
  
  // 測試 TTS
  console.log('\n🔊 測試 TTS 功能...');
  results.tests.tts = TTS模型正確測試_debug();
  results.summary.total++;
  if (results.tests.tts.success) {
    results.summary.passed++;
  } else {
    results.summary.failed++;
  }
  
  // 測試對話音頻
  console.log('\n🎙️ 測試對話音頻功能...');
  results.tests.audioDialog = 對話音頻模型正確測試_debug();
  results.summary.total++;
  if (results.tests.audioDialog.success) {
    results.summary.passed++;
  } else {
    results.summary.failed++;
  }
  
  // 輸出摘要
  console.log('\n📊 測試摘要:');
  console.log(`總計: ${results.summary.total} 項測試`);
  console.log(`通過: ${results.summary.passed} 項`);
  console.log(`失敗: ${results.summary.failed} 項`);
  
  if (results.summary.passed === 0) {
    console.log('\n❌ 所有語音功能都不可用，可能原因：');
    console.log('1. API 金鑰沒有語音功能權限');
    console.log('2. 需要申請 Gemini 語音功能 Beta 存取權限');
    console.log('3. 地區限制或配額問題');
  }
  
  console.log('\n🎉 ===== 完整語音功能測試完成 =====');
  return results;
}
