/*
 * 檔案: API金鑰與語音模型診斷_debug.gs
 * 分類: test
 * 功能: 診斷 API 金鑰解析和語音模型配置問題
 * 描述: 檢查 API 金鑰是否正確分割，顯示當前使用的金鑰，檢查語音模型配置
 * 最後更新: 2025-07-13
 */

/**
 * 🔍 API 金鑰與語音模型完整診斷
 * 檢查金鑰解析、語音模型配置、API 版本等問題
 */
function API金鑰與語音模型診斷_debug() {
  console.log('🔍 ===== API 金鑰與語音模型診斷開始 =====');
  
  const diagnosis = {
    timestamp: new Date().toISOString(),
    apiKeyAnalysis: {},
    voiceModelAnalysis: {},
    recommendations: []
  };
  
  try {
    // ===== 1. API 金鑰解析診斷 =====
    console.log('\n📋 1. API 金鑰解析診斷...');
    
    const keyString = getConfigValue('geminiApiKey');
    console.log(`原始金鑰字串長度: ${keyString ? keyString.length : 0}`);
    
    if (keyString) {
      // 檢查是否包含換行符
      const hasCarriageReturn = keyString.includes('\r');
      const hasNewline = keyString.includes('\n');
      const hasComma = keyString.includes(',');
      
      console.log(`包含 \\r: ${hasCarriageReturn}`);
      console.log(`包含 \\n: ${hasNewline}`);
      console.log(`包含逗號: ${hasComma}`);
      
      // 解析金鑰
      const parsedKeys = parseMultipleApiKeys(keyString);
      console.log(`解析後金鑰數量: ${parsedKeys.length}`);
      
      // 顯示每組金鑰的前後字符（隱藏中間部分）
      parsedKeys.forEach((key, index) => {
        const maskedKey = maskApiKey(key);
        console.log(`第 ${index + 1} 組金鑰: ${maskedKey} (長度: ${key.length})`);
      });
      
      // 獲取當前使用的金鑰
      const currentKey = getCurrentGeminiApiKey();
      const currentMasked = maskApiKey(currentKey);
      console.log(`當前使用金鑰: ${currentMasked}`);
      
      diagnosis.apiKeyAnalysis = {
        totalKeys: parsedKeys.length,
        hasCarriageReturn,
        hasNewline,
        hasComma,
        currentKeyMasked: currentMasked,
        keyLengths: parsedKeys.map(key => key.length),
        parseSuccess: parsedKeys.length > 0
      };
      
    } else {
      console.log('❌ 未找到 Gemini API Key');
      diagnosis.apiKeyAnalysis.error = '未找到 Gemini API Key';
    }
    
    // ===== 2. 語音模型配置診斷 =====
    console.log('\n🎙️ 2. 語音模型配置診斷...');
    
    const config = getConfig();
    const ttsModel = config.ttsModel;
    const audioDialogModel = config.audioDialogModel;
    
    console.log(`TTS 模型: ${ttsModel}`);
    console.log(`對話音頻模型: ${audioDialogModel}`);
    
    // 檢查模型 API 版本
    const ttsApiVersion = getModelApiVersion(ttsModel);
    const audioDialogApiVersion = getModelApiVersion(audioDialogModel);
    
    console.log(`TTS 模型 API 版本: ${ttsApiVersion}`);
    console.log(`對話音頻模型 API 版本: ${audioDialogApiVersion}`);
    
    // 檢查模型配置
    const ttsConfig = getModelConfig(ttsModel);
    const audioDialogConfig = getModelConfig(audioDialogModel);
    
    console.log(`TTS 模型配置:`, JSON.stringify(ttsConfig, null, 2));
    console.log(`對話音頻模型配置:`, JSON.stringify(audioDialogConfig, null, 2));
    
    diagnosis.voiceModelAnalysis = {
      ttsModel,
      audioDialogModel,
      ttsApiVersion,
      audioDialogApiVersion,
      ttsConfig,
      audioDialogConfig
    };
    
    // ===== 3. 模型存在性測試 =====
    console.log('\n🧪 3. 模型存在性測試...');
    
    // 測試對話音頻模型（這是出現 404 錯誤的模型）
    const testResult = testModelExistence(audioDialogModel, audioDialogApiVersion, currentKey);
    console.log(`對話音頻模型測試結果:`, JSON.stringify(testResult, null, 2));
    
    diagnosis.voiceModelAnalysis.modelTest = testResult;
    
    // ===== 4. 生成建議 =====
    if (!diagnosis.apiKeyAnalysis.parseSuccess) {
      diagnosis.recommendations.push('檢查 APIKEY 工作表中的 Gemini API Key 格式');
    }
    
    if (diagnosis.apiKeyAnalysis.totalKeys === 0) {
      diagnosis.recommendations.push('添加有效的 Gemini API Key');
    }
    
    if (testResult && !testResult.success) {
      if (testResult.statusCode === 404) {
        diagnosis.recommendations.push(`模型 ${audioDialogModel} 在 API 版本 ${audioDialogApiVersion} 中不存在，建議更換模型或檢查模型名稱`);
      } else {
        diagnosis.recommendations.push(`模型測試失敗 (${testResult.statusCode})，檢查 API 金鑰權限或模型配置`);
      }
    }
    
    console.log('\n📊 診斷完成，結果:');
    console.log(JSON.stringify(diagnosis, null, 2));
    
    return diagnosis;
    
  } catch (error) {
    console.error('❌ 診斷過程發生錯誤:', error);
    diagnosis.error = error.message;
    return diagnosis;
  }
}

/**
 * 🔒 遮罩 API 金鑰（顯示前後幾個字符，中間用 x 替代）
 * @param {string} apiKey - 原始 API 金鑰
 * @returns {string} 遮罩後的金鑰
 */
function maskApiKey(apiKey) {
  if (!apiKey || apiKey.length < 10) {
    return 'INVALID_KEY';
  }
  
  const start = apiKey.substring(0, 6);
  const end = apiKey.substring(apiKey.length - 4);
  const middle = 'x'.repeat(apiKey.length - 10);
  
  return `${start}${middle}${end}`;
}

/**
 * 🧪 測試模型是否存在
 * @param {string} modelName - 模型名稱
 * @param {string} apiVersion - API 版本
 * @param {string} apiKey - API 金鑰
 * @returns {object} 測試結果
 */
function testModelExistence(modelName, apiVersion, apiKey) {
  try {
    console.log(`🧪 測試模型: ${modelName} (API 版本: ${apiVersion})`);
    
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${apiKey}`;
    
    // 使用最簡單的測試請求
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: '測試' }]
      }]
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    const statusCode = response.getResponseCode();
    const responseText = response.getContentText();
    
    console.log(`模型測試回應代碼: ${statusCode}`);
    
    if (statusCode === 200) {
      return {
        success: true,
        statusCode,
        message: '模型存在且可正常調用'
      };
    } else {
      let errorMessage = '未知錯誤';
      try {
        const errorData = JSON.parse(responseText);
        errorMessage = errorData.error?.message || responseText;
      } catch (e) {
        errorMessage = responseText;
      }
      
      return {
        success: false,
        statusCode,
        message: errorMessage,
        fullResponse: responseText
      };
    }
    
  } catch (error) {
    console.error('❌ 模型測試失敗:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 🚀 快速檢查_debug
 * 快速檢查當前狀態
 */
function 快速檢查_debug() {
  console.log('🚀 ===== 快速檢查開始 =====');
  
  try {
    // 檢查 API 金鑰
    const currentKey = getCurrentGeminiApiKey();
    console.log(`當前金鑰: ${maskApiKey(currentKey)}`);
    
    // 檢查語音模型
    const config = getConfig();
    console.log(`語音模型: ${config.audioDialogModel}`);
    console.log(`API 版本: ${getModelApiVersion(config.audioDialogModel)}`);
    
    // 檢查輪換索引
    const rotationIndex = getApiKeyRotationIndex();
    console.log(`輪換索引: ${rotationIndex}`);
    
    console.log('\n🎉 ===== 快速檢查完成 =====');
    
  } catch (error) {
    console.error('❌ 快速檢查失敗:', error);
  }
}
