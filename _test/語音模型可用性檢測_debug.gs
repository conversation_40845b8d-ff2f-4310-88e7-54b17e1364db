/*
 * 檔案: 語音模型可用性檢測_debug.gs
 * 分類: test
 * 功能: 檢測語音模型的實際可用性
 * 描述: 測試官方文檔中的語音模型是否真的可以調用
 * 最後更新: 2025-07-13
 */

/**
 * 🔍 語音模型可用性完整檢測
 * 測試所有語音相關模型的實際可用性
 */
function 語音模型可用性檢測_debug() {
  console.log('🔍 ===== 語音模型可用性檢測開始 =====');
  
  const testResults = {
    timestamp: new Date().toISOString(),
    apiKeyStatus: null,
    modelTests: [],
    summary: {
      total: 0,
      available: 0,
      unavailable: 0
    },
    recommendations: []
  };
  
  try {
    // ===== 1. API 金鑰檢查 =====
    console.log('\n📋 1. API 金鑰檢查...');
    
    const currentKey = getCurrentGeminiApiKey();
    const maskedKey = maskApiKey(currentKey);
    console.log(`當前使用金鑰: ${maskedKey}`);
    testResults.apiKeyStatus = { success: true, maskedKey };
    
    // ===== 2. 語音模型清單 =====
    const voiceModels = [
      // TTS 模型
      'gemini-2.5-flash-preview-tts',
      'gemini-2.5-pro-preview-tts',
      
      // 對話音頻模型
      'gemini-2.5-flash-preview-native-audio-dialog',
      'gemini-2.5-flash-exp-native-audio-thinking-dialog',
      
      // Live API 模型
      'gemini-live-2.5-flash-preview',
      'gemini-2.0-flash-live-001'
    ];
    
    console.log(`\n🎙️ 2. 測試 ${voiceModels.length} 個語音模型...`);
    
    // ===== 3. 逐一測試模型 =====
    for (const modelName of voiceModels) {
      console.log(`\n🧪 測試模型: ${modelName}`);
      
      const testResult = testModelAvailability(modelName, currentKey);
      testResults.modelTests.push(testResult);
      testResults.summary.total++;
      
      if (testResult.available) {
        testResults.summary.available++;
        console.log(`✅ ${modelName}: 可用`);
      } else {
        testResults.summary.unavailable++;
        console.log(`❌ ${modelName}: 不可用 (${testResult.statusCode})`);
      }
    }
    
    // ===== 4. 生成建議 =====
    console.log('\n📊 4. 生成建議...');
    
    const availableModels = testResults.modelTests
      .filter(test => test.available)
      .map(test => test.modelName);
    
    if (availableModels.length === 0) {
      testResults.recommendations.push('❌ 沒有可用的語音模型，請檢查 API 金鑰權限');
    } else {
      testResults.recommendations.push(`✅ 發現 ${availableModels.length} 個可用語音模型`);
      
      // 推薦最佳模型
      const bestTtsModel = availableModels.find(model => model.includes('tts'));
      const bestDialogModel = availableModels.find(model => model.includes('dialog'));
      
      if (bestTtsModel) {
        testResults.recommendations.push(`🔊 推薦 TTS 模型: ${bestTtsModel}`);
      }
      
      if (bestDialogModel) {
        testResults.recommendations.push(`🎙️ 推薦對話模型: ${bestDialogModel}`);
      }
    }
    
    // ===== 5. 輸出結果 =====
    console.log('\n📊 檢測完成，結果摘要:');
    console.log(`總計: ${testResults.summary.total} 個模型`);
    console.log(`可用: ${testResults.summary.available} 個`);
    console.log(`不可用: ${testResults.summary.unavailable} 個`);
    
    console.log('\n💡 建議:');
    testResults.recommendations.forEach(rec => console.log(`  ${rec}`));
    
    console.log('\n📋 詳細結果:');
    console.log(JSON.stringify(testResults, null, 2));
    
    return testResults;
    
  } catch (error) {
    console.error('❌ 檢測過程發生錯誤:', error);
    testResults.error = error.message;
    return testResults;
  }
}

/**
 * 🧪 測試單個模型的可用性
 * @param {string} modelName - 模型名稱
 * @param {string} apiKey - API 金鑰
 * @returns {object} 測試結果
 */
function testModelAvailability(modelName, apiKey) {
  const result = {
    modelName,
    available: false,
    statusCode: null,
    apiVersion: null,
    error: null,
    responseTime: null
  };
  
  try {
    const startTime = Date.now();
    
    // 獲取模型的 API 版本
    const apiVersion = getModelApiVersion(modelName);
    result.apiVersion = apiVersion;
    
    const url = `https://generativelanguage.googleapis.com/${apiVersion}/models/${modelName}:generateContent?key=${apiKey}`;
    
    // 使用最簡單的測試請求
    const payload = JSON.stringify({
      contents: [{
        parts: [{ text: '測試' }]
      }]
    });
    
    const response = UrlFetchApp.fetch(url, {
      method: 'POST',
      contentType: 'application/json',
      payload: payload,
      muteHttpExceptions: true
    });
    
    const endTime = Date.now();
    result.responseTime = endTime - startTime;
    result.statusCode = response.getResponseCode();
    
    if (result.statusCode === 200) {
      result.available = true;
    } else {
      // 嘗試解析錯誤訊息
      try {
        const errorData = JSON.parse(response.getContentText());
        result.error = errorData.error?.message || '未知錯誤';
      } catch (e) {
        result.error = response.getContentText();
      }
    }
    
  } catch (error) {
    result.error = error.message;
  }
  
  return result;
}

/**
 * 🔒 遮罩 API 金鑰（顯示前後幾個字符，中間用 x 替代）
 * @param {string} apiKey - 原始 API 金鑰
 * @returns {string} 遮罩後的金鑰
 */
function maskApiKey(apiKey) {
  if (!apiKey || apiKey.length < 10) {
    return 'INVALID_KEY';
  }
  
  const start = apiKey.substring(0, 6);
  const end = apiKey.substring(apiKey.length - 4);
  const middle = 'x'.repeat(apiKey.length - 10);
  
  return `${start}${middle}${end}`;
}

/**
 * 🚀 快速語音模型檢查_debug
 * 快速檢查當前配置的語音模型狀態
 */
function 快速語音模型檢查_debug() {
  console.log('🚀 ===== 快速語音模型檢查開始 =====');
  
  try {
    const config = getConfig();
    
    // 檢查當前配置的模型
    const ttsModel = config.ttsModel;
    const audioDialogModel = config.audioDialogModel;
    
    console.log(`TTS 模型: ${ttsModel}`);
    console.log(`對話音頻模型: ${audioDialogModel}`);
    
    // 測試這兩個模型
    const currentKey = getCurrentGeminiApiKey();
    
    console.log('\n🧪 測試 TTS 模型...');
    const ttsResult = testModelAvailability(ttsModel, currentKey);
    console.log(`TTS 結果: ${ttsResult.available ? '✅ 可用' : '❌ 不可用'} (${ttsResult.statusCode})`);
    
    console.log('\n🧪 測試對話音頻模型...');
    const dialogResult = testModelAvailability(audioDialogModel, currentKey);
    console.log(`對話音頻結果: ${dialogResult.available ? '✅ 可用' : '❌ 不可用'} (${dialogResult.statusCode})`);
    
    if (!dialogResult.available) {
      console.log(`❌ 對話音頻模型錯誤: ${dialogResult.error}`);
    }
    
    console.log('\n🎉 ===== 快速檢查完成 =====');
    
    return {
      tts: ttsResult,
      dialog: dialogResult
    };
    
  } catch (error) {
    console.error('❌ 快速檢查失敗:', error);
  }
}

/**
 * 🔧 修復語音模型配置_debug
 * 根據可用性檢測結果自動修復語音模型配置
 */
function 修復語音模型配置_debug() {
  console.log('🔧 ===== 修復語音模型配置開始 =====');
  
  try {
    // 先檢測可用模型
    const testResults = 語音模型可用性檢測_debug();
    
    const availableModels = testResults.modelTests
      .filter(test => test.available)
      .map(test => test.modelName);
    
    if (availableModels.length === 0) {
      console.log('❌ 沒有可用的語音模型，無法修復');
      return false;
    }
    
    // 找到最佳的可用模型
    const bestTtsModel = availableModels.find(model => model.includes('tts'));
    const bestDialogModel = availableModels.find(model => model.includes('dialog'));
    
    console.log('\n🔧 建議的模型配置:');
    if (bestTtsModel) {
      console.log(`TTS 模型: ${bestTtsModel}`);
    }
    if (bestDialogModel) {
      console.log(`對話音頻模型: ${bestDialogModel}`);
    }
    
    console.log('\n⚠️ 請手動更新 APIKEY 工作表中的模型配置');
    console.log('🎉 ===== 修復建議完成 =====');
    
    return {
      recommendedTts: bestTtsModel,
      recommendedDialog: bestDialogModel,
      availableModels
    };
    
  } catch (error) {
    console.error('❌ 修復過程失敗:', error);
    return false;
  }
}
